# 琼海气象数据录入Qdrant向量数据库的完整实现
import pandas as pd
import numpy as np
from datetime import datetime
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct
from sentence_transformers import SentenceTransformer
from sklearn.preprocessing import StandardScaler
import openpyxl
import json
from typing import List, Dict, Any
import warnings
warnings.filterwarnings('ignore')

class QionghaiWeatherVectorDB:
    def __init__(self, host="localhost", port=6333):
        """初始化琼海气象数据向量数据库"""
        self.client = QdrantClient(host=host, port=port)
        self.collection_name = "qionghai_weather_2024"
        self.embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        self.scaler = StandardScaler()
        print("✅ Qdrant客户端初始化完成")
    
    def load_excel_data(self, file_path: str) -> pd.DataFrame:
        """加载并处理Excel文件中的气象数据"""
        print("📊 正在加载Excel数据...")
        
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        all_data = []
        
        # 处理每个月份的工作表
        for sheet_name in excel_file.sheet_names:
            print(f"   处理{sheet_name}月份数据...")
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # 找到表头行
            header_row = None
            for i, row in df.iterrows():
                if pd.notna(row.iloc[0]) and str(row.iloc[0]) == '日期':
                    header_row = i
                    break
            
            if header_row is not None:
                # 重新读取，从表头行开始
                df_clean = pd.read_excel(file_path, sheet_name=sheet_name, 
                                       skiprows=header_row, header=0)
                
                # 过滤有效数据行
                df_clean = df_clean[df_clean['日期'].notna()]
                df_clean = df_clean[pd.to_numeric(df_clean['日期'], errors='coerce').notna()]
                
                # 添加月份信息
                month_num = sheet_name.strip().replace('月', '')
                try:
                    month_num = int(month_num)
                except:
                    # 如果不能直接转换，根据位置推断
                    month_num = excel_file.sheet_names.index(sheet_name) + 1
                
                df_clean['月份'] = month_num
                df_clean['年份'] = 2024
                
                # 创建完整日期
                df_clean['完整日期'] = pd.to_datetime(
                    df_clean.apply(lambda x: f"2024-{month_num:02d}-{int(x['日期']):02d}", axis=1)
                )
                
                all_data.append(df_clean)
        
        # 合并所有月份数据
        weather_data = pd.concat(all_data, ignore_index=True)
        print(f"✅ 数据加载完成，共{len(weather_data)}条记录")
        return weather_data
    
    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """预处理气象数据"""
        print("🔄 正在预处理数据...")
        
        processed_data = data.copy()
        
        # 标准化列名
        column_mapping = {
            '降雨量（mm）': 'rainfall',
            '平均气温(℃)': 'avg_temp',
            '最高气温(℃)': 'max_temp', 
            '最低气温(℃)': 'min_temp',
            '本站气压(hpa)': 'pressure',
            '相对湿度（%）': 'humidity'
        }
        
        for old_name, new_name in column_mapping.items():
            if old_name in processed_data.columns:
                processed_data[new_name] = pd.to_numeric(processed_data[old_name], errors='coerce')
        
        # 处理缺失值
        processed_data['rainfall'] = processed_data['rainfall'].fillna(0)
        for col in ['avg_temp', 'max_temp', 'min_temp', 'pressure', 'humidity']:
            if col in processed_data.columns:
                processed_data[col] = processed_data[col].fillna(processed_data[col].mean())
        
        # 创建派生特征
        processed_data['temp_range'] = processed_data['max_temp'] - processed_data['min_temp']
        processed_data['has_rain'] = (processed_data['rainfall'] > 0).astype(int)
        
        # 时间特征
        processed_data['day_of_week'] = processed_data['完整日期'].dt.dayofweek
        processed_data['is_weekend'] = (processed_data['day_of_week'] >= 5).astype(int)
        processed_data['day_of_year'] = processed_data['完整日期'].dt.dayofyear
        
        # 季节划分
        season_map = {12: 4, 1: 4, 2: 4, 3: 1, 4: 1, 5: 1, 
                     6: 2, 7: 2, 8: 2, 9: 3, 10: 3, 11: 3}
        processed_data['season'] = processed_data['月份'].map(season_map)
        
        # 天气类型分类
        def classify_weather(row):
            temp, rain, humidity = row['avg_temp'], row['rainfall'], row['humidity']
            if rain > 25: return "暴雨天"
            elif rain > 0.1: return "雨天" 
            elif temp > 35: return "酷热天"
            elif temp < 10: return "寒冷天"
            elif humidity > 85: return "潮湿天"
            else: return "舒适天"
        
        processed_data['weather_type'] = processed_data.apply(classify_weather, axis=1)
        
        # 温度等级
        processed_data['temp_level'] = pd.cut(processed_data['avg_temp'], 
                                            bins=[-np.inf, 15, 25, 32, np.inf],
                                            labels=['低温', '适温', '高温', '极高温'])
        
        print("✅ 数据预处理完成")
        return processed_data
    
    def create_embeddings(self, data: pd.DataFrame) -> tuple:
        """创建向量表示"""
        print("🔮 正在创建向量表示...")
        
        # 创建结构化描述文本
        descriptions = []
        for _, row in data.iterrows():
            season_names = {1: '春季', 2: '夏季', 3: '秋季', 4: '冬季'}
            weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            
            desc = f"""琼海国家气象站天气记录
日期：{row['完整日期'].strftime('%Y年%m月%d日')} {weekdays[row['day_of_week']]}
季节：{season_names.get(row['season'], '未知')}

温度状况：
平均气温 {row['avg_temp']:.1f}°C，最高 {row['max_temp']:.1f}°C，最低 {row['min_temp']:.1f}°C
日温差 {row['temp_range']:.1f}°C，温度等级：{row['temp_level']}

降水情况：
{'有降雨' if row['has_rain'] else '无降雨'}，降雨量 {row['rainfall']:.1f}毫米

大气环境：
气压 {row['pressure']:.1f}百帕，相对湿度 {row['humidity']:.1f}%

天气特征：{row['weather_type']}"""
            
            descriptions.append(desc)
        
        # 生成文本向量
        print("   正在编码文本向量...")
        embeddings = self.embedding_model.encode(descriptions, show_progress_bar=True)
        
        print("✅ 向量创建完成")
        return embeddings, descriptions
    
    def setup_collection(self, vector_size: int = 384):
        """设置Qdrant集合"""
        print("🏗️ 正在设置Qdrant集合...")
        
        try:
            # 删除已存在的集合（如果有）
            collections = self.client.get_collections()
            if self.collection_name in [col.name for col in collections.collections]:
                self.client.delete_collection(self.collection_name)
                print("   已删除旧集合")
            
            # 创建新集合
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=vector_size,
                    distance=Distance.COSINE
                ),
                # 优化配置
                hnsw_config=models.HnswConfig(
                    m=16,
                    ef_construct=100,
                    full_scan_threshold=10000
                )
            )
            
            # 创建索引
            self._create_indexes()
            print("✅ 集合设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 集合设置失败: {e}")
            return False
    
    def _create_indexes(self):
        """创建字段索引"""
        index_fields = [
            ("date", models.PayloadSchemaType.DATETIME),
            ("avg_temp", models.PayloadSchemaType.FLOAT),
            ("max_temp", models.PayloadSchemaType.FLOAT), 
            ("min_temp", models.PayloadSchemaType.FLOAT),
            ("rainfall", models.PayloadSchemaType.FLOAT),
            ("pressure", models.PayloadSchemaType.FLOAT),
            ("humidity", models.PayloadSchemaType.FLOAT),
            ("season", models.PayloadSchemaType.INTEGER),
            ("month", models.PayloadSchemaType.INTEGER),
            ("weather_type", models.PayloadSchemaType.KEYWORD),
            ("temp_level", models.PayloadSchemaType.KEYWORD),
            ("has_rain", models.PayloadSchemaType.BOOL)
        ]
        
        for field_name, field_type in index_fields:
            try:
                self.client.create_payload_index(
                    collection_name=self.collection_name,
                    field_name=field_name,
                    field_schema=field_type
                )
            except:
                pass  # 索引可能已存在
    
    def insert_data(self, data: pd.DataFrame, embeddings: np.ndarray, 
                   descriptions: List[str], batch_size: int = 100):
        """批量插入数据"""
        print("📥 正在插入数据到Qdrant...")
        
        points = []
        for i, (_, row) in enumerate(data.iterrows()):
            payload = {
                "date": row['完整日期'].isoformat(),
                "description": descriptions[i],
                "avg_temp": float(row['avg_temp']),
                "max_temp": float(row['max_temp']),
                "min_temp": float(row['min_temp']),
                "rainfall": float(row['rainfall']),
                "pressure": float(row['pressure']),
                "humidity": float(row['humidity']),
                "temp_range": float(row['temp_range']),
                "season": int(row['season']),
                "month": int(row['月份']),
                "day_of_week": int(row['day_of_week']),
                "day_of_year": int(row['day_of_year']),
                "is_weekend": bool(row['is_weekend']),
                "has_rain": bool(row['has_rain']),
                "weather_type": str(row['weather_type']),
                "temp_level": str(row['temp_level'])
            }
            
            point = PointStruct(
                id=f"weather_{row['完整日期'].strftime('%Y%m%d')}",
                vector=embeddings[i].tolist(),
                payload=payload
            )
            points.append(point)
        
        # 批量插入
        try:
            for i in range(0, len(points), batch_size):
                batch = points[i:i + batch_size]
                result = self.client.upsert(
                    collection_name=self.collection_name,
                    points=batch
                )
                print(f"   已插入批次 {i//batch_size + 1}/{(len(points)-1)//batch_size + 1}")
            
            # 验证插入结果
            info = self.client.get_collection(self.collection_name)
            print(f"✅ 数据插入完成！总向量数: {info.points_count}")
            return True
            
        except Exception as e:
            print(f"❌ 数据插入失败: {e}")
            return False
    
    def semantic_search(self, query: str, limit: int = 10, score_threshold: float = 0.6):
        """语义搜索"""
        print(f"🔍 执行语义搜索: {query}")
        
        # 编码查询
        query_vector = self.embedding_model.encode([query])[0]
        
        try:
            results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector.tolist(),
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True
            )
            
            print(f"找到 {len(results)} 条相关记录")
            return self._format_results(results)
            
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return []
    
    def filter_search(self, filters: Dict[str, Any], limit: int = 50):
        """条件筛选搜索"""
        print(f"🎯 执行条件筛选: {filters}")
        
        try:
            # 构建过滤条件
            conditions = []
            for key, value in filters.items():
                if isinstance(value, dict):
                    if "gte" in value:
                        conditions.append(
                            models.FieldCondition(
                                key=key,
                                range=models.Range(gte=value["gte"])
                            )
                        )
                    elif "lte" in value:
                        conditions.append(
                            models.FieldCondition(
                                key=key,
                                range=models.Range(lte=value["lte"])
                            )
                        )
                    elif "gt" in value and "lt" in value:
                        conditions.append(
                            models.FieldCondition(
                                key=key,
                                range=models.Range(gt=value["gt"], lt=value["lt"])
                            )
                        )
                else:
                    conditions.append(
                        models.FieldCondition(
                            key=key,
                            match=models.MatchValue(value=value)
                        )
                    )
            
            filter_condition = models.Filter(must=conditions) if conditions else None
            
            results, _ = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=filter_condition,
                limit=limit,
                with_payload=True
            )
            
            print(f"找到 {len(results)} 条匹配记录")
            return self._format_scroll_results(results)
            
        except Exception as e:
            print(f"❌ 筛选失败: {e}")
            return []
    
    def _format_results(self, results):
        """格式化搜索结果"""
        formatted = []
        for result in results:
            formatted.append({
                "score": round(result.score, 4),
                "date": result.payload["date"],
                "weather_type": result.payload["weather_type"],
                "avg_temp": result.payload["avg_temp"],
                "rainfall": result.payload["rainfall"],
                "humidity": result.payload["humidity"],
                "description": result.payload["description"][:150] + "..."
            })
        return formatted
    
    def _format_scroll_results(self, results):
        """格式化滚动结果"""
        formatted = []
        for result in results:
            formatted.append({
                "date": result.payload["date"],
                "weather_type": result.payload["weather_type"],
                "avg_temp": result.payload["avg_temp"],
                "rainfall": result.payload["rainfall"],
                "humidity": result.payload["humidity"],
                "description": result.payload["description"][:150] + "..."
            })
        return formatted

# 使用示例和测试代码
def main():
    """主程序：完整的数据处理和插入流程"""
    
    # 1. 初始化向量数据库
    weather_db = QionghaiWeatherVectorDB(host="localhost", port=6333)
    
    # 2. 加载Excel数据（请替换为你的实际文件路径）
    excel_file_path = "2024年琼海国家站逐日气象要素统计.xlsx"
    try:
        raw_data = weather_db.load_excel_data(excel_file_path)
    except Exception as e:
        print(f"❌ 文件加载失败: {e}")
        return
    
    # 3. 数据预处理
    processed_data = weather_db.preprocess_data(raw_data)
    
    # 4. 创建向量表示
    embeddings, descriptions = weather_db.create_embeddings(processed_data)
    
    # 5. 设置Qdrant集合
    if not weather_db.setup_collection(vector_size=embeddings.shape[1]):
        return
    
    # 6. 插入数据
    if not weather_db.insert_data(processed_data, embeddings, descriptions):
        return
    
    print("\n🎉 数据录入完成！开始测试查询功能...\n")
    
    # 7. 测试查询功能
    test_queries(weather_db)

def test_queries(weather_db: QionghaiWeatherVectorDB):
    """测试各种查询功能"""
    
    print("=" * 60)
    print("🧪 测试语义搜索")
    print("=" * 60)
    
    # 语义搜索测试
    queries = [
        "寻找炎热且潮湿的夏季天气",
        "查找有大雨的天气情况", 
        "找到温度适宜的春季天气",
        "寻找极端高温天气"
    ]
    
    for query in queries:
        results = weather_db.semantic_search(query, limit=3)
        print(f"\n查询: {query}")
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result['date']} | {result['weather_type']} | "
                  f"温度:{result['avg_temp']}°C | 降雨:{result['rainfall']}mm | "
                  f"相似度:{result['score']}")
    
    print("\n" + "=" * 60)
    print("🎯 测试条件筛选")
    print("=" * 60)
    
    # 条件筛选测试
    filter_tests = [
        {"max_temp": {"gte": 35}, "season": 2},  # 夏季高温
        {"rainfall": {"gte": 10}, "weather_type": "暴雨天"},  # 暴雨天
        {"season": 1, "temp_level": "适温"},  # 春季适温
        {"has_rain": True, "humidity": {"gte": 90}}  # 高湿度降雨
    ]
    
    descriptions = [
        "夏季高温天气 (最高温度>=35°C)",
        "暴雨天气 (降雨量>=10mm)", 
        "春季适温天气",
        "高湿度降雨天气 (湿度>=90%)"
    ]
    
    for filters, desc in zip(filter_tests, descriptions):
        results = weather_db.filter_search(filters, limit=5)
        print(f"\n{desc}: 找到 {len(results)} 条记录")
        for i, result in enumerate(results[:3], 1):
            print(f"  {i}. {result['date']} | {result['weather_type']} | "
                  f"温度:{result['avg_temp']}°C | 降雨:{result['rainfall']}mm")

if __name__ == "__main__":
    main()
