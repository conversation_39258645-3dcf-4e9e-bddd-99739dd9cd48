import React from "react";
import { 
  CloudRain, 
  Thermometer, 
  Drop, 
  Wind,
  Sun,
  TrendUp,
  Calendar,
  MapPin
} from "@phosphor-icons/react";

export default function WeatherDataPanel() {
  return (
    <div className="w-full bg-theme-bg-secondary rounded-xl p-6">
      <div className="flex items-center gap-2 mb-6">
        <CloudRain size={24} className="text-blue-400" />
        <h3 className="text-xl font-semibold text-white">气象数据显示</h3>
      </div>
      
      {/* 实时气象数据卡片 */}
      <div className="grid grid-cols-4 gap-3 mb-6">
        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 rounded-lg p-4 text-center hover:bg-theme-action-menu-item-hover transition-all duration-300">
          <Thermometer size={24} className="text-red-400 mx-auto mb-2" />
          <p className="text-theme-text-secondary text-xs mb-1">温度</p>
          <p className="text-white text-lg font-semibold">33°C</p>
        </div>
        
        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 rounded-lg p-4 text-center hover:bg-theme-action-menu-item-hover transition-all duration-300">
          <Drop size={24} className="text-blue-400 mx-auto mb-2" />
          <p className="text-theme-text-secondary text-xs mb-1">湿度</p>
          <p className="text-white text-lg font-semibold">69%</p>
        </div>
        
        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 rounded-lg p-4 text-center hover:bg-theme-action-menu-item-hover transition-all duration-300">
          <Wind size={24} className="text-green-400 mx-auto mb-2" />
          <p className="text-theme-text-secondary text-xs mb-1">风速</p>
          <p className="text-white text-lg font-semibold">12km/h</p>
        </div>
        
        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 rounded-lg p-4 text-center hover:bg-theme-action-menu-item-hover transition-all duration-300">
          <Sun size={24} className="text-yellow-400 mx-auto mb-2" />
          <p className="text-theme-text-secondary text-xs mb-1">紫外线</p>
          <p className="text-white text-lg font-semibold">中等</p>
        </div>
      </div>
      
      {/* 气象趋势和位置信息 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover rounded-lg p-4 cursor-pointer transition-all duration-300">
          <div className="flex items-center gap-3 mb-2">
            <TrendUp size={18} className="text-blue-400" />
            <span className="text-white font-medium">今日趋势</span>
          </div>
          <p className="text-theme-text-secondary text-sm">
            气温逐渐上升，适宜户外康养活动
          </p>
        </div>
        
        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover rounded-lg p-4 cursor-pointer transition-all duration-300">
          <div className="flex items-center gap-3 mb-2">
            <MapPin size={18} className="text-blue-400" />
            <span className="text-white font-medium">当前位置</span>
          </div>
          <p className="text-theme-text-secondary text-sm">
            海南省琼海市 - 空气质量良好
          </p>
        </div>
      </div>
    </div>
  );
}