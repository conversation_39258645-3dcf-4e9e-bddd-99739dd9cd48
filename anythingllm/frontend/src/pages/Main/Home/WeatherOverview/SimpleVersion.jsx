import React from "react";
import { 
  Sun, 
  Thermometer, 
  Eye, 
  Speedometer,
  TrendUp,
  Calendar
} from "@phosphor-icons/react";

export default function WeatherOverview() {
  return (
    <div className="w-full bg-theme-bg-secondary rounded-xl border border-theme-modal-border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-white mb-2">系统介绍区域</h2>
          <p className="text-theme-text-secondary text-sm">
            MHIIS医疗气候康养智能分析系统功能概览
          </p>
        </div>
      </div>

      {/* 主要功能区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        
        {/* 气象相关快捷链接 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <Sun size={20} className="text-blue-400" />
            <h3 className="text-lg font-medium text-white">气象相关快捷链接</h3>
          </div>
          
          <div 
            className="bg-theme-settings-input-bg hover:bg-theme-action-menu-item-hover 
                       rounded-lg p-6 cursor-pointer transition-all duration-300 border border-theme-modal-border/50"
          >
            <div className="flex items-center gap-3 mb-3">
              <TrendUp size={18} className="text-blue-400" />
              <span className="text-white font-medium">气象数据实时监控</span>
            </div>
            <p className="text-theme-text-secondary text-sm">
              实时获取和分析温度、湿度、气压等气象数据，为康养决策提供科学依据
            </p>
          </div>

          <div 
            className="bg-theme-settings-input-bg hover:bg-theme-action-menu-item-hover 
                       rounded-lg p-6 cursor-pointer transition-all duration-300 border border-theme-modal-border/50"
          >
            <div className="flex items-center gap-3 mb-3">
              <Speedometer size={18} className="text-blue-400" />
              <span className="text-white font-medium">气象预测分析</span>
            </div>
            <p className="text-theme-text-secondary text-sm">
              基于历史数据和AI模型，预测未来气象变化趋势，辅助康养计划制定
            </p>
          </div>
        </div>

        {/* 快捷链接区域 */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <Eye size={20} className="text-blue-400" />
            <h3 className="text-lg font-medium text-white">快捷链接区域（现有）</h3>
          </div>
          
          <div 
            className="bg-theme-settings-input-bg hover:bg-theme-action-menu-item-hover 
                       rounded-lg p-6 cursor-pointer transition-all duration-300 border border-theme-modal-border/50"
          >
            <div className="flex items-center gap-3 mb-3">
              <Calendar size={18} className="text-blue-400" />
              <span className="text-white font-medium">工作区管理</span>
            </div>
            <p className="text-theme-text-secondary text-sm">
              创建和管理不同的分析工作区，组织您的气象和康养数据
            </p>
          </div>

          <div 
            className="bg-theme-settings-input-bg hover:bg-theme-action-menu-item-hover 
                       rounded-lg p-6 cursor-pointer transition-all duration-300 border border-theme-modal-border/50"
          >
            <div className="flex items-center gap-3 mb-3">
              <Thermometer size={18} className="text-blue-400" />
              <span className="text-white font-medium">智能分析报告</span>
            </div>
            <p className="text-theme-text-secondary text-sm">
              生成专业的气象康养分析报告，支持多种数据可视化形式
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}