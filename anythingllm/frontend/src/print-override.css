/* 专门针对printer-friendly扩展的CSS覆盖 */
@media print {
  /* 页面设置 */
  @page {
    margin: 20mm;
    @bottom-center {
      content: "© 2025 海南长小养智能科技有限责任公司 - 医疗气候康养智能分析系统 MHIIS";
      font-size: 10px;
      color: #666;
    }
  }
  
  /* 强制侧边栏高度自适应 */
  .bg-theme-bg-sidebar {
    height: auto !important;
    min-height: 100% !important;
  }
  
  /* 确保所有嵌套容器也自适应高度 */
  .bg-theme-bg-sidebar > div,
  .bg-theme-bg-sidebar .flex,
  .bg-theme-bg-sidebar .flex-col,
  .bg-theme-bg-sidebar .flex-grow,
  .bg-theme-bg-sidebar .flex-1 {
    height: auto !important;
    min-height: auto !important;
  }
  
  /* 移除overflow限制 */
  .bg-theme-bg-sidebar .overflow-y-scroll,
  .bg-theme-bg-sidebar .overflow-hidden {
    overflow: visible !important;
  }
  
  /* 主容器自适应高度 */
  .w-screen.h-screen {
    height: auto !important;
  }
  
  /* 版权信息固定在页面绝对底部 */
  .print-copyright {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100vw !important;
    height: 40px !important;
    background: white !important;
    border-top: 1px solid #ddd !important;
    padding: 10px 0 !important;
    text-align: center !important;
    font-size: 10px !important;
    color: #666 !important;
    z-index: 10000 !important;
    display: block !important;
    line-height: 20px !important;
  }
}

/* 强制所有情况下的覆盖 */
@media screen {
  body.print-mode .bg-theme-bg-sidebar,
  body[data-print="true"] .bg-theme-bg-sidebar,
  .print-preview .bg-theme-bg-sidebar {
    height: auto !important;
    min-height: 100% !important;
  }
}