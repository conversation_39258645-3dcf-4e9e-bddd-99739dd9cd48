/**
 * 将UTC时间转换为北京时间格式
 * @param {string|Date} dateString - UTC时间字符串或Date对象
 * @param {boolean} showTime - 是否显示时间，默认为true
 * @returns {string} 格式化的北京时间字符串
 */
export function formatBeijingTime(dateString, showTime = true) {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '-';
    }
    
    // 设置为北京时区 (UTC+8)
    const options = {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      ...(showTime && {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })
    };
    
    // 使用中文本地化格式
    const formatter = new Intl.DateTimeFormat('zh-CN', options);
    let formatted = formatter.format(date);
    
    // 如果只显示日期，返回 YYYY-MM-DD 格式
    if (!showTime) {
      // 转换为标准的YYYY-MM-DD格式
      const parts = formatted.split('/');
      if (parts.length === 3) {
        return `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`;
      }
      return formatted;
    }
    
    // 如果显示时间，格式为：2025-07-24 10:19:18
    // 处理中文格式的日期和时间
    if (formatted.includes('年') && formatted.includes('月') && formatted.includes('日')) {
      // 中文格式：2025年7月24日 10:19:18
      formatted = formatted
        .replace(/年/g, '-')
        .replace(/月/g, '-')
        .replace(/日/g, '');
    } else {
      // 如果是其他格式，尝试标准化
      formatted = formatted.replace(/\//g, '-');
    }
    
    return formatted;
  } catch (error) {
    console.error('Date formatting error:', error);
    return '-';
  }
}

/**
 * 获取相对时间描述（如：5分钟前、昨天、3天前等）
 * @param {string|Date} dateString - UTC时间字符串或Date对象
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(dateString) {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '-';
    }
    
    const now = new Date();
    const diffMs = now - date;
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffSeconds < 60) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7);
      return `${weeks}周前`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months}个月前`;
    } else {
      const years = Math.floor(diffDays / 365);
      return `${years}年前`;
    }
  } catch (error) {
    console.error('Relative time error:', error);
    return '-';
  }
}

/**
 * 格式化日期为年月日格式
 * @param {string|Date} dateString - UTC时间字符串或Date对象
 * @returns {string} 格式化的日期字符串（如：2025年7月24日）
 */
export function formatChineseDate(dateString) {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '-';
    }
    
    const options = {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    
    return new Intl.DateTimeFormat('zh-CN', options).format(date);
  } catch (error) {
    console.error('Chinese date formatting error:', error);
    return '-';
  }
}