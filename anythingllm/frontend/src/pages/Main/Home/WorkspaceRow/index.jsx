import React from "react";
import { ChatText, Users, Calendar } from "@phosphor-icons/react";
import paths from "@/utils/paths";
import { formatBeijingTime } from "@/utils/dateFormatter";

export default function WorkspaceRow({ workspace, onClick }) {
  const handleClick = () => {
    if (onClick) {
      onClick(workspace);
    } else {
      // 默认行为：跳转到工作区聊天
      window.location.href = paths.workspace.chat(workspace.slug);
    }
  };

  return (
    <div
      className="bg-theme-bg-secondary border border-theme-modal-border/30 rounded-lg p-4 hover:bg-theme-action-menu-item-hover transition-all duration-300 cursor-pointer group flex items-center justify-between"
      onClick={handleClick}
    >
      {/* 左侧：工作区信息 */}
      <div className="flex items-center gap-4 flex-1">
        <div className="p-2 bg-theme-bg-secondary rounded-lg group-hover:bg-primary-button group-hover:text-black transition-all duration-300">
          <ChatText
            size={18}
            className="text-theme-text-primary group-hover:text-black"
          />
        </div>
        
        <div className="flex-1">
          <h3 className="text-base font-semibold text-white mb-1 group-hover:text-primary-button transition-colors">
            {workspace.name}
          </h3>
          <p className="text-theme-text-secondary text-xs line-clamp-1">
            {workspace.description || "暂无描述"}
          </p>
        </div>
      </div>

      {/* 右侧：统计信息 */}
      <div className="flex items-center gap-6 text-xs text-theme-text-secondary">
        <div className="flex items-center gap-1">
          <Users size={12} />
          <span>{workspace.chats?.length || 0} 对话</span>
        </div>
        <div className="flex items-center gap-1">
          <Calendar size={12} />
          <span>
            {workspace.createdAt
              ? formatBeijingTime(workspace.createdAt, false)
              : "最近创建"}
          </span>
        </div>
        <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
          →
        </div>
      </div>
    </div>
  );
}