# 气象数据录入Qdrant向量数据库完整方案

## 1. 环境配置和依赖安装

```bash
# 安装必要的依赖
pip install qdrant-client pandas numpy scikit-learn sentence-transformers openpyxl
```

```python
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sentence_transformers import SentenceTransformer
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import json
from typing import List, Dict, Any
import uuid
```

## 2. Qdrant客户端初始化和配置

```python
class WeatherQdrantManager:
    def __init__(self, host="localhost", port=6333, collection_name="qionghai_weather_2024"):
        """
        初始化Qdrant客户端
        """
        self.client = QdrantClient(host=host, port=port)
        self.collection_name = collection_name
        self.embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        self.scaler = StandardScaler()
        self.is_collection_created = False
        
    def create_collection(self, vector_size=384, distance_metric=Distance.COSINE):
        """
        创建Qdrant集合，针对气象数据优化
        """
        try:
            # 检查集合是否已存在
            collections = self.client.get_collections()
            existing_collections = [col.name for col in collections.collections]
            
            if self.collection_name in existing_collections:
                print(f"集合 {self.collection_name} 已存在，将使用现有集合")
                self.is_collection_created = True
                return True
            
            # 创建集合配置
            self.client.recreate_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=vector_size,
                    distance=distance_metric
                ),
                # 优化配置
                optimizers_config=models.OptimizersConfig(
                    deleted_threshold=0.2,
                    vacuum_min_vector_number=1000,
                    default_segment_number=0,
                    max_segment_size=None,
                    memmap_threshold=None,
                    indexing_threshold=20000,
                    flush_interval_sec=5,
                    max_optimization_threads=1
                ),
                # HNSW索引配置，适合气象数据查询
                hnsw_config=models.HnswConfig(
                    m=16,  # 连接数，影响查询精度和速度
                    ef_construct=100,  # 构建时的候选数
                    full_scan_threshold=10000,  # 全扫描阈值
                    max_indexing_threads=0,
                    on_disk=False  # 对于气象数据，内存存储更快
                )
            )
            print(f"成功创建集合: {self.collection_name}")
            self.is_collection_created = True
            return True
            
        except Exception as e:
            print(f"创建集合失败: {e}")
            return False
    
    def create_payload_index(self):
        """
        为气象数据的关键字段创建索引，提高查询性能
        """
        try:
            # 为日期创建索引
            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="date",
                field_schema=models.PayloadSchemaType.DATETIME
            )
            
            # 为数值字段创建索引
            numeric_fields = [
                "avg_temp", "max_temp", "min_temp", "rainfall", 
                "pressure", "humidity", "season", "month"
            ]
            
            for field in numeric_fields:
                self.client.create_payload_index(
                    collection_name=self.collection_name,
                    field_name=field,
                    field_schema=models.PayloadSchemaType.FLOAT
                )
            
            # 为分类字段创建索引
            categorical_fields = ["is_weekend", "weather_type", "temperature_level"]
            for field in categorical_fields:
                self.client.create_payload_index(
                    collection_name=self.collection_name,
                    field_name=field,
                    field_schema=models.PayloadSchemaType.KEYWORD
                )
            
            print("索引创建完成")
            return True
            
        except Exception as e:
            print(f"创建索引失败: {e}")
            return False
```

## 3. 数据预处理和特征工程（针对气象数据优化）

```python
def preprocess_weather_data(data):
    """
    专门针对气象数据的预处理
    """
    # 深拷贝数据避免修改原数据
    processed_data = data.copy()
    
    # 处理缺失值 - 使用前后值插值，更适合气象数据的连续性
    numeric_columns = ['降雨量（mm）', '平均气温(℃)', '最高气温(℃)', 
                      '最低气温(℃)', '本站气压(hpa)', '相对湿度（%）']
    
    for col in numeric_columns:
        if col in processed_data.columns:
            # 降雨量缺失值填0，其他用插值
            if '降雨' in col:
                processed_data[col] = processed_data[col].fillna(0)
            else:
                processed_data[col] = processed_data[col].interpolate(method='linear')
                # 如果仍有缺失值，用均值填充
                processed_data[col] = processed_data[col].fillna(processed_data[col].mean())
    
    # 数据类型转换
    for col in numeric_columns:
        if col in processed_data.columns:
            processed_data[col] = pd.to_numeric(processed_data[col], errors='coerce')
    
    return processed_data

def create_advanced_weather_features(data):
    """
    创建高级气象特征，提高向量表示的丰富度
    """
    # 确保完整日期是datetime类型
    data['完整日期'] = pd.to_datetime(data['完整日期'])
    
    # 基础派生特征
    data['温差'] = data['最高气温(℃)'] - data['最低气温(℃)']
    data['是否降雨'] = (data['降雨量（mm）'] > 0).astype(int)
    data['降雨强度'] = pd.cut(data['降雨量（mm）'], 
                           bins=[0, 0.1, 10, 25, 50, float('inf')],
                           labels=['无雨', '小雨', '中雨', '大雨', '暴雨'])
    
    # 时间特征
    data['星期几'] = data['完整日期'].dt.dayofweek
    data['是否周末'] = (data['星期几'] >= 5).astype(int)
    data['一年中的第几天'] = data['完整日期'].dt.dayofyear
    data['季节'] = data['月份'].map({12:4, 1:4, 2:4, 3:1, 4:1, 5:1, 
                                   6:2, 7:2, 8:2, 9:3, 10:3, 11:3})
    
    # 温度分级
    data['温度等级'] = pd.cut(data['平均气温(℃)'], 
                           bins=[-float('inf'), 10, 20, 30, float('inf')],
                           labels=['低温', '适温', '高温', '极高温'])
    
    # 湿度舒适度
    data['湿度舒适度'] = pd.cut(data['相对湿度（%）'], 
                             bins=[0, 40, 60, 80, 100],
                             labels=['干燥', '舒适', '潮湿', '很潮湿'])
    
    # 滑动窗口特征（捕捉天气变化趋势）
    window_sizes = [3, 7]  # 3天和7天窗口
    for window in window_sizes:
        # 温度趋势
        data[f'{window}日平均气温'] = data['平均气温(℃)'].rolling(window=window, center=True).mean()
        data[f'{window}日气温变异'] = data['平均气温(℃)'].rolling(window=window, center=True).std()
        
        # 降雨趋势
        data[f'{window}日累计降雨'] = data['降雨量（mm）'].rolling(window=window, center=True).sum()
        data[f'{window}日降雨天数'] = data['是否降雨'].rolling(window=window, center=True).sum()
        
        # 湿度和气压趋势
        data[f'{window}日平均湿度'] = data['相对湿度（%）'].rolling(window=window, center=True).mean()
        data[f'{window}日平均气压'] = data['本站气压(hpa)'].rolling(window=window, center=True).mean()
    
    # 天气类型综合判断
    def classify_weather_type(row):
        temp = row['平均气温(℃)']
        rain = row['降雨量（mm）']
        humidity = row['相对湿度（%）']
        
        if rain > 25:
            return "暴雨天"
        elif rain > 0.1:
            if temp > 25:
                return "温热雨天"
            else:
                return "凉雨天"
        elif humidity > 80:
            if temp > 30:
                return "闷热天"
            else:
                return "潮湿天"
        elif temp > 35:
            return "酷热天"
        elif temp < 10:
            return "寒冷天"
        else:
            return "舒适天"
    
    data['天气类型'] = data.apply(classify_weather_type, axis=1)
    
    return data

def create_weather_vectors(data, manager: WeatherQdrantManager):
    """
    创建针对气象数据优化的向量表示
    """
    # 1. 数值特征向量化
    numerical_features = [
        '降雨量（mm）', '平均气温(℃)', '最高气温(℃)', '最低气温(℃)',
        '本站气压(hpa)', '相对湿度（%）', '温差', '星期几', '一年中的第几天',
        '3日平均气温', '3日气温变异', '3日累计降雨', '3日降雨天数',
        '7日平均气温', '7日气温变异', '7日累计降雨', '7日降雨天数'
    ]
    
    # 过滤存在的特征
    available_features = [f for f in numerical_features if f in data.columns]
    numerical_data = data[available_features].fillna(0)
    
    # 标准化数值特征
    numerical_vectors = manager.scaler.fit_transform(numerical_data)
    
    # 2. 创建结构化的天气描述文本
    descriptions = []
    for _, row in data.iterrows():
        desc = f"""
        琼海气象站天气记录
        日期：{row['完整日期'].strftime('%Y年%m月%d日')} ({['周一','周二','周三','周四','周五','周六','周日'][row['星期几']]})
        季节：{['','春季','夏季','秋季','冬季'][row['季节']]}
        
        温度情况：
        - 平均气温：{row['平均气温(℃)']}°C
        - 最高气温：{row['最高气温(℃)']}°C
        - 最低气温：{row['最低气温(℃)']}°C
        - 日温差：{row['温差']:.1f}°C
        - 温度等级：{row['温度等级']}
        
        降水情况：
        - {'有降雨' if row['是否降雨'] else '无降雨'}
        - 降雨量：{row['降雨量（mm）']}毫米
        - 降雨强度：{row['降雨强度']}
        
        大气环境：
        - 大气压：{row['本站气压(hpa)']}百帕
        - 相对湿度：{row['相对湿度（%）']}%
        - 湿度舒适度：{row['湿度舒适度']}
        
        天气特征：{row['天气类型']}
        """
        descriptions.append(desc.strip())
    
    # 3. 文本向量化
    text_vectors = manager.embedding_model.encode(descriptions, show_progress_bar=True)
    
    # 4. 融合数值向量和文本向量（可选）
    # 这里我们主要使用文本向量，因为它包含了更丰富的语义信息
    
    return text_vectors, descriptions, numerical_vectors
```

## 4. 数据录入Qdrant实现

```python
def batch_insert_weather_data(manager: WeatherQdrantManager, data, batch_size=100):
    """
    批量插入气象数据到Qdrant
    """
    if not manager.is_collection_created:
        if not manager.create_collection():
            return False
        manager.create_payload_index()
    
    # 数据预处理和特征工程
    print("正在进行数据预处理...")
    processed_data = preprocess_weather_data(data)
    processed_data = create_advanced_weather_features(processed_data)
    
    # 创建向量
    print("正在创建向量表示...")
    text_vectors, descriptions, numerical_vectors = create_weather_vectors(processed_data, manager)
    
    # 准备数据点
    points = []
    for i, (_, row) in enumerate(processed_data.iterrows()):
        # 创建丰富的元数据
        payload = {
            # 基础信息
            "date": row['完整日期'].isoformat(),
            "description": descriptions[i],
            
            # 气象数据
            "rainfall": float(row['降雨量（mm）']),
            "avg_temp": float(row['平均气温(℃)']),
            "max_temp": float(row['最高气温(℃)']),
            "min_temp": float(row['最低气温(℃)']),
            "pressure": float(row['本站气压(hpa)']),
            "humidity": float(row['相对湿度（%）']),
            "temp_range": float(row['温差']),
            
            # 时间特征
            "season": int(row['季节']),
            "month": int(row['月份']),
            "day_of_week": int(row['星期几']),
            "is_weekend": bool(row['是否周末']),
            "day_of_year": int(row['一年中的第几天']),
            
            # 分类特征
            "weather_type": str(row['天气类型']),
            "temperature_level": str(row['温度等级']),
            "humidity_comfort": str(row['湿度舒适度']),
            "rain_intensity": str(row['降雨强度']),
            "has_rain": bool(row['是否降雨']),
            
            # 趋势特征（如果存在的话）
            **({f"temp_3d_avg": float(row['3日平均气温'])} if pd.notna(row.get('3日平均气温')) else {}),
            **({f"rain_3d_total": float(row['3日累计降雨'])} if pd.notna(row.get('3日累计降雨')) else {}),
            **({f"temp_7d_avg": float(row['7日平均气温'])} if pd.notna(row.get('7日平均气温')) else {}),
            **({f"rain_7d_total": float(row['7日累计降雨'])} if pd.notna(row.get('7日累计降雨')) else {}),
        }
        
        # 创建数据点
        point = PointStruct(
            id=f"weather_{row['完整日期'].strftime('%Y%m%d')}",
            vector=text_vectors[i].tolist(),
            payload=payload
        )
        points.append(point)
    
    # 批量插入
    print(f"正在批量插入 {len(points)} 条记录...")
    try:
        for i in range(0, len(points), batch_size):
            batch = points[i:i + batch_size]
            result = manager.client.upsert(
                collection_name=manager.collection_name,
                points=batch
            )
            print(f"已插入批次 {i//batch_size + 1}, 状态: {result.status}")
        
        # 获取集合信息
        info = manager.client.get_collection(manager.collection_name)
        print(f"插入完成！集合总向量数: {info.points_count}")
        return True
        
    except Exception as e:
        print(f"插入数据失败: {e}")
        return False
```

## 5. 高级查询功能实现

```python
class WeatherQueryEngine:
    def __init__(self, manager: WeatherQdrantManager):
        self.manager = manager
        self.client = manager.client
        self.collection_name = manager.collection_name
        self.embedding_model = manager.embedding_model
    
    def semantic_search(self, query_text: str, limit: int = 10, score_threshold: float = 0.7):
        """
        语义搜索：根据自然语言描述查找相似天气
        """
        # 向量化查询文本
        query_vector = self.embedding_model.encode([query_text])[0]
        
        try:
            results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector.tolist(),
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True,
                with_vectors=False
            )
            
            return self._format_search_results(results, "语义搜索")
            
        except Exception as e:
            print(f"语义搜索失败: {e}")
            return []
    
    def filter_search(self, filters: Dict[str, Any], limit: int = 50):
        """
        条件筛选：根据具体条件筛选天气数据
        """
        try:
            # 构建Qdrant过滤条件
            filter_conditions = self._build_filter_conditions(filters)
            
            results = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=filter_conditions,
                limit=limit,
                with_payload=True,
                with_vectors=False
            )
            
            return self._format_scroll_results(results[0], "条件筛选")
            
        except Exception as e:
            print(f"条件筛选失败: {e}")
            return []
    
    def hybrid_search(self, query_text: str, filters: Dict[str, Any] = None, 
                     limit: int = 10, score_threshold: float = 0.6):
        """
        混合搜索：结合语义搜索和条件筛选
        """
        query_vector = self.embedding_model.encode([query_text])[0]
        
        try:
            search_params = {
                "collection_name": self.collection_name,
                "query_vector": query_vector.tolist(),
                "limit": limit,
                "score_threshold": score_threshold,
                "with_payload": True,
                "with_vectors": False
            }
            
            # 添加过滤条件
            if filters:
                search_params["query_filter"] = self._build_filter_conditions(filters)
            
            results = self.client.search(**search_params)
            return self._format_search_results(results, "混合搜索")
            
        except Exception as e:
            print(f"混合搜索失败: {e}")
            return []
    
    def find_weather_patterns(self, reference_date: str, pattern_type: str = "similar", 
                            days_range: int = 30, limit: int = 10):
        """
        天气模式查找：找到与参考日期相似或相反的天气模式
        """
        try:
            # 获取参考日期的天气数据
            ref_results = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="date",
                            match=models.MatchValue(value=reference_date)
                        )
                    ]
                ),
                limit=1,
                with_payload=True,
                with_vectors=True
            )
            
            if not ref_results[0]:
                return []
            
            ref_point = ref_results[0][0]
            ref_vector = ref_point.vector
            
            # 根据模式类型调整搜索策略
            if pattern_type == "similar":
                # 寻找相似天气
                results = self.client.search(
                    collection_name=self.collection_name,
                    query_vector=ref_vector,
                    limit=limit + 1,  # +1 因为会包含参考日期本身
                    with_payload=True
                )
                # 移除参考日期本身
                results = [r for r in results if r.payload["date"] != reference_date]
                
            else:  # opposite
                # 寻找相反天气模式（这里需要更复杂的逻辑）
                # 可以通过反向向量或特定条件来实现
                results = []
            
            return self._format_search_results(results[:limit], f"{pattern_type}模式搜索")
            
        except Exception as e:
            print(f"天气模式查找失败: {e}")
            return []
    
    def _build_filter_conditions(self, filters: Dict[str, Any]):
        """
        构建Qdrant过滤条件
        """
        conditions = []
        
        for key, value in filters.items():
            if isinstance(value, dict):
                # 范围查询
                if "gte" in value:
                    conditions.append(
                        models.FieldCondition(
                            key=key,
                            range=models.Range(gte=value["gte"])
                        )
                    )
                elif "lte" in value:
                    conditions.append(
                        models.FieldCondition(
                            key=key,
                            range=models.Range(lte=value["lte"])
                        )
                    )
                elif "gt" in value and "lt" in value:
                    conditions.append(
                        models.FieldCondition(
                            key=key,
                            range=models.Range(gt=value["gt"], lt=value["lt"])
                        )
                    )
            elif isinstance(value, list):
                # 多值匹配
                conditions.append(
                    models.FieldCondition(
                        key=key,
                        match=models.MatchAny(any=value)
                    )
                )
            else:
                # 精确匹配
                conditions.append(
                    models.FieldCondition(
                        key=key,
                        match=models.MatchValue(value=value)
                    )
                )
        
        return models.Filter(must=conditions) if conditions else None
    
    def _format_search_results(self, results, search_type: str):
        """
        格式化搜索结果
        """
        formatted_results = []
        for result in results:
            formatted_results.append({
                "search_type": search_type,
                "score": getattr(result, 'score', None),
                "date": result.payload.get("date"),
                "weather_type": result.payload.get("weather_type"),
                "avg_temp": result.payload.get("avg_temp"),
                "rainfall": result.payload.get("rainfall"),
                "humidity": result.payload.get("humidity"),
                "description": result.payload.get("description", "")[:200] + "..."
            })
        return formatted_results
    
    def _format_scroll_results(self, results, search_type: str):
        """
        格式化滚动查询结果
        """
        formatted_results = []
        for result in results:
            formatted_results.append({
                "search_type": search_type,
                "date": result.payload.get("date"),
                "weather_type": result.payload.get("weather_type"),
                "avg_temp": result.payload.get("avg_temp"),
                "rainfall": result.payload.get("rainfall"),
                "humidity": result.payload.get("humidity"),
                "description": result.payload.get("description", "")[:200] + "..."
            })
        return formatted_results
```

## 6. 实际使用示例

```python
# 完整的使用流程示例
def main_weather_workflow():
    # 1. 初始化Qdrant管理器
    weather_manager = WeatherQdrantManager(
        host="localhost", 
        port=6333, 
        collection_name="qionghai_weather_2024"
    )
    
    # 2. 加载和处理Excel数据
    # 这里需要你的实际数据处理逻辑
    # data = load_weather_excel_data("2024年琼海国家站逐日气象要素统计.xlsx")
    
    # 3. 批量插入数据
    # success = batch_insert_weather_data(weather_manager, data)
    
    # 4. 创建查询引擎
    query_engine = WeatherQueryEngine(weather_manager)
    
    # 5. 各种查询示例
    
    # 语义搜索示例
    semantic_results = query_engine.semantic_search(
        "寻找炎热夏季且有降雨的天气", 
        limit=5
    )
    print("语义搜索结果:", semantic_results)
    
    # 条件筛选示例
    filter_results = query_engine.filter_search({
        "max_temp": {"gte": 35},  # 最高温度>=35℃
        "season": 2,  # 夏季
        "has_rain": True  # 有降雨
    })
    print("条件筛选结果:", filter_results)
    
    # 混合搜索示例
    hybrid_results = query_engine.hybrid_search(
        "舒适的春季天气",
        filters={"season": 1, "temperature_level": "适温"},
        limit=5
    )
    print("混合搜索结果:", hybrid_results)
    
    # 天气模式查找示例
    pattern_results = query_engine.find_weather_patterns(
        reference_date="2024-07-15",
        pattern_type="similar",
        limit=5
    )
    print("相似天气模式:", pattern_results)

# 高级分析功能
def advanced_weather_analytics(query_engine: WeatherQueryEngine):
    """
    高级天气分析功能
    """
    # 1. 极端天气事件分析
    extreme_hot = query_engine.filter_search({
        "max_temp": {"gte": 38}
    })
    print(f"发现 {len(extreme_hot)} 个极端高温天气事件")
    
    # 2. 季节性降雨模式
    seasons = [1, 2, 3, 4]
    for season in seasons:
        rainy_days = query_engine.filter_search({
            "season": season,
            "has_rain": True
        })
        season_names = ["", "春季", "夏季", "秋季", "冬季"]
        print(f"{season_names[season]}降雨天数: {len(rainy_days)}")
    
    # 3. 特殊天气组合查询
    special_weather = query_engine.filter_search({
        "weather_type": ["暴雨天", "闷热天", "酷热天"]
    })
    print(f"特殊天气事件: {len(special_weather)} 次")

if __name__ == "__main__":
    main_weather_workflow()
```

## 7. Qdrant性能优化配置

```python
# Docker Compose配置文件示例 (docker-compose.yml)
version: '3.8'
services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - ./qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=INFO
    # 针对气象数据的内存配置
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

# 生产环境配置建议
production_config = {
    "service": {
        "max_request_size_mb": 32,
        "max_workers": 0,  # 自动检测CPU核心数
        "enable_cors": True
    },
    "storage": {
        "performance": {
            "max_search_threads": 0,
            "max_optimization_threads": 1
        }
    },
    "cluster": {
        "enabled": False  # 单节点部署
    }
}
```

## 8. 监控和维护

```python
def monitor_qdrant_performance(manager: WeatherQdrantManager):
    """
    监控Qdrant性能指标
    """
    try:
        # 获取集合信息
        collection_info = manager.client.get_collection(manager.collection_name)
        
        print("=== Qdrant集合状态 ===")
        print(f"向量总数: {collection_info.points_count}")
        print(f"向量维度: {collection_info.config.params.vectors.size}")
        print(f"距离度量: {collection_info.config.params.vectors.distance}")
        
        # 获取集合统计信息
        cluster_info = manager.client.get_cluster_info()
        print(f"\n=== 集群信息 ===")
        print(f"节点ID: {cluster_info.peer_id}")
        print(f"集群状态: {cluster_info.status}")
        
        return True
        
    except Exception as e:
        print(f"监控获取失败: {e}")
        return False

def backup_collection(manager: WeatherQdrantManager, backup_path: str):
    """
    备份集合数据
    """
    try:
        # 创建快照
        snapshot_info = manager.client.create_snapshot(
            collection_name=manager.collection_name
        )
        print(f"创建快照成功: {snapshot_info}")
        return True
        
    except Exception as e:
        print(f"备份失败: {e}")
        return False
```

这个针对Qdrant优化的方案提供了：

1. **专门的Qdrant配置**：针对气象数据的索引和存储优化
2. **高性能批量插入**：使用批量操作提高写入效率
3. **丰富的查询功能**：语义搜索、条件筛选、混合查询等
4. **智能索引策略**：为常用查询字段创建专门索引
5. **生产级别的配置**：包含Docker部署和性能监控

你想先从哪个部分开始实施？我可以帮你调整具体的实现细节。