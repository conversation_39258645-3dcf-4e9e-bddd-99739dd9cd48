# 医疗气候康养智能分析系统 (MHIIS) 技术栈说明文档

**文档创建日期：** 2025-07-24  
**最后更新日期：** 2025-07-24  
**版本：** v1.0  
**系统版本：** 1.8.4  

---

## 项目概述

医疗气候康养智能分析系统 (Medical Healthcare Intelligent Insight System, MHIIS) 是基于AnythingLLM构建的专业化智能分析平台，专注于医疗、气候和康养领域的智能问答和数据分析服务。

**项目指导单位：** 琼海市气象局、琼海长养康宁医院  
**开发单位：** 海南长小养智能科技有限责任公司

---

## 核心架构

### 系统架构模式
- **架构模式：** 微服务架构 + RAG (检索增强生成)
- **部署模式：** Docker容器化部署
- **数据库架构：** 混合数据库架构 (多数据库协同)
- **AI架构：** LLM + 向量数据库 + 知识图谱

### 主要组件
```
MHIIS System
├── Frontend (React + Vite)
├── Backend API (Node.js + Express)
├── Document Processor (Collector Service)
├── Knowledge Graph (Graphiti + Neo4j)
├── Vector Storage (Qdrant)
├── Database Cluster (PostgreSQL + Redis + InfluxDB)
└── AI Integration (Multiple LLM Providers)
```

---

## 前端技术栈

### 核心框架
- **React** `18.2.0` - 主前端框架
- **Vite** `4.3.0` - 构建工具和开发服务器
- **React Router DOM** `6.3.0` - 路由管理

### UI/UX 技术
- **TailwindCSS** `3.3.1` - CSS框架，自定义医疗主题
- **@phosphor-icons/react** `2.1.7` - 图标库
- **@tremor/react** `3.15.1` - 数据可视化组件
- **react-toastify** `9.1.3` - 通知组件

### 国际化支持
- **i18next** `23.11.3` - 国际化框架
- **react-i18next** `14.1.1` - React国际化集成
- **i18next-browser-languagedetector** `7.2.1` - 语言检测
- **支持语言：** 简体中文（主要）、繁体中文、英语等20+语言

### 专业功能组件
- **@hello-pangea/dnd** `16.6.1` - 拖拽功能
- **react-dropzone** `14.2.3` - 文件上传
- **recharts** `2.12.5` - 图表可视化
- **react-speech-recognition** `3.10.0` - 语音识别
- **katex** `0.6.0` - 数学公式渲染
- **highlight.js** `11.9.0` - 代码高亮

### 开发工具
- **TypeScript** - 类型支持
- **ESLint** `8.50.0` - 代码质量检查
- **Prettier** `3.0.3` - 代码格式化
- **Flow** - 静态类型检查

---

## 后端技术栈

### 核心框架
- **Node.js** `≥18.12.1` - 运行时环境
- **Express.js** - Web应用框架
- **@mintplex-labs/express-ws** `5.0.7` - WebSocket支持

### 数据库与ORM
- **Prisma** `5.3.1` - 数据库ORM
- **@prisma/client** - Prisma客户端
- **bcrypt** `5.1.0` - 密码加密

### AI集成框架
- **LangChain生态系统：**
  - `@langchain/core` `0.1.61` - 核心组件
  - `@langchain/community` `0.0.53` - 社区组件
  - `@langchain/anthropic` `0.1.16` - Anthropic集成
  - `@langchain/openai` `0.0.28` - OpenAI集成
  - `@langchain/aws` `0.0.5` - AWS集成
  - `@langchain/textsplitters` - 文本分割器

### LLM提供商支持
- **OpenAI Integration** - GPT系列模型
- **Anthropic SDK** `0.39.0` - Claude系列模型
- **AWS Bedrock** `3.775.0` - AWS托管模型
- **Cohere AI** `7.9.5` - Cohere模型
- **本地模型支持** - Ollama等

### 向量数据库集成
- **Qdrant** `@qdrant/js-client-rest` `1.9.0` - 主向量数据库
- **Pinecone** `@pinecone-database/pinecone` `2.0.1`
- **Chroma** `chromadb` `2.0.1`
- **Milvus** `@zilliz/milvus2-sdk-node` `2.3.5`
- **LanceDB** `@lancedb/lancedb` `0.15.0`

### 文档处理
- **@xenova/transformers** `2.14.0` - 机器学习模型
- **cheerio** `1.0.0` - HTML解析
- **adm-zip** `0.5.16` - ZIP文件处理
- **apache-arrow** `19.0.0` - 列式数据格式

### 任务调度与后台服务
- **@mintplex-labs/bree** `9.2.5` - 任务调度
- **@ladjs/graceful** `3.2.2` - 优雅关闭

### MCP (Model Context Protocol)
- **@modelcontextprotocol/sdk** `1.11.0` - 模型上下文协议

---

## 数据库架构

### 主数据库 - PostgreSQL
- **版本：** PostgreSQL 15 Alpine
- **端口：** 5499
- **用途：** 主业务数据存储
- **特性：**
  - 27个业务表结构
  - UTF-8编码支持
  - 健康检查机制
  - 数据持久化

### 向量数据库 - Qdrant
- **版本：** Latest
- **端口：** 6199 (HTTP), 6198 (gRPC)
- **用途：** 高性能语义搜索
- **特性：**
  - 支持多种向量相似度算法
  - 实时索引更新
  - 集群支持

### 时序数据库 - InfluxDB
- **版本：** 2.7 Alpine
- **端口：** 8099
- **用途：** 气象健康指标存储
- **特性：**
  - 365天数据保留策略
  - 专用气象数据桶
  - 高性能时序查询

### 知识图谱 - Neo4j
- **版本：** 5.26 Community
- **端口：** 7499 (HTTP), 7699 (Bolt)
- **用途：** Graphiti时序知识图谱
- **特性：**
  - APOC插件支持
  - Graph Data Science支持
  - 向量搜索功能
  - 2GB堆内存配置

### 缓存数据库 - Redis
- **版本：** 7 Alpine
- **端口：** 6299
- **用途：** 缓存和会话管理
- **特性：**
  - AOF持久化
  - 密码保护
  - 健康检查

### 消息队列 - RabbitMQ
- **版本：** 3.12 Management Alpine
- **端口：** 5699 (AMQP), 15699 (管理界面)
- **用途：** 异步任务处理
- **特性：**
  - 专用虚拟主机
  - 管理界面
  - 数据持久化

---

## Graphiti 知识图谱技术

### 环境配置
- **Python版本：** 3.12.9
- **包管理器：** UV (高性能Python包管理器)
- **虚拟环境：** `.venv`

### 核心实体类型
- **WeatherEntity** - 气象数据实体
- **WellnessIndexEntity** - 康养指数实体
- **UserEntity** - 用户画像实体
- **RegionEntity** - 地理区域实体

### 集成架构
```
AnythingLLM (RAG) ←→ Graphiti (Knowledge Graph) ←→ Neo4j
                 ↓                                    ↓
            Qdrant (Vector)                    Temporal Relations
```

### 特性
- 时序感知的知识图谱构建
- 气象-健康关系的智能分析
- 个性化康养推荐
- 混合检索 (语义+关键词+图遍历)
- 实时增量更新

---

## 容器化部署

### Docker配置
- **Docker Compose版本：** 3.8
- **网络：** 自定义桥接网络 (mhiis-network)
- **数据持久化：** Docker卷管理

### 服务编排
```yaml
Services:
├── postgres (主数据库)
├── qdrant (向量数据库)
├── influxdb (时序数据库)
├── neo4j (图数据库)
├── redis (缓存)
├── rabbitmq (消息队列)
└── adminer (数据库管理工具)
```

### 健康检查
- 所有关键服务配置健康检查
- 自动重启策略 (unless-stopped)
- 服务依赖管理

---

## 开发工具链

### 包管理
- **Frontend：** Yarn
- **Backend：** NPM/Yarn
- **Python：** UV

### 代码质量
- **ESLint** - JavaScript/TypeScript代码检查
- **Prettier** - 代码格式化
- **Flow** - 静态类型检查

### 构建工具
- **Vite** - 前端构建和开发服务器
- **Rollup** - 模块打包器
- **PostCSS** - CSS处理
- **Autoprefixer** - CSS前缀自动添加

### 监控与日志
- **日志轮转：** 10MB自动轮转，保留5个历史文件
- **日志分类：** 服务端日志、收集器日志
- **日志级别：** info, warn, error
- **管理脚本：** `./scripts/manage-logs.sh`

---

## 安全特性

### 认证与授权
- **多用户模式支持**
- **角色基础访问控制** (admin, manager, default)
- **密码加密存储** (bcrypt)
- **会话管理** (Redis)

### 数据安全
- **数据库密码保护**
- **API密钥管理**
- **CORS配置**
- **输入验证和清理**

### 网络安全
- **Docker网络隔离**
- **端口映射管理**
- **健康检查机制**

---

## 性能优化

### 前端优化
- **Vite热更新**
- **代码分割**
- **懒加载**
- **图片优化**
- **缓存策略**

### 后端优化
- **数据库连接池**
- **Redis缓存**
- **API响应缓存**
- **向量索引优化**

### 数据库优化
- **PostgreSQL查询优化**
- **Qdrant向量索引**
- **Redis内存优化**
- **InfluxDB时序优化**

---

## 国际化支持

### 语言支持
- **主要语言：** 简体中文
- **次要语言：** 繁体中文、英语
- **扩展支持：** 20+种语言

### 本地化功能
- **界面翻译**
- **日期时间格式**
- **数字格式**
- **语言自动检测**

---

## 开发环境要求

### 系统要求
- **操作系统：** macOS, Linux, Windows
- **Docker：** 20.10+
- **Docker Compose：** 2.0+
- **Node.js：** 18.12.1+
- **Python：** 3.12.9+

### 开发工具推荐
- **IDE：** VS Code, WebStorm
- **数据库客户端：** Adminer (已集成), DBeaver
- **API测试：** Postman, Insomnia
- **Git客户端：** Git CLI, GitHub Desktop

---

## 部署架构

### 开发环境
```bash
# 数据库服务启动
docker-compose -f docker-compose.databases.yml up -d

# 应用服务启动
yarn dev:all  # 同时启动前端、后端、收集器
```

### 生产环境
```bash
# 构建前端
yarn prod:frontend

# 启动生产服务
yarn prod:server
```

---

## 监控与维护

### 日志管理
- **日志路径：** `logs/server/`, `logs/collector/`
- **轮转策略：** 10MB/5个历史文件
- **清理策略：** 7天自动清理

### 健康检查
- **数据库连接检查**
- **API服务检查**
- **向量数据库检查**
- **知识图谱检查**

### 备份策略
- **数据库自动备份**
- **Docker卷备份**
- **配置文件版本控制**

---

## 版本历史

### v1.8.4 (当前版本 - 2025-07-24)
- 完成中文界面本地化
- 集成Graphiti知识图谱
- 升级数据库架构为分布式
- 实现医疗主题UI设计
- 完善日志管理系统
- 统一背景主题为医疗绿色渐变
- 优化侧边栏和登录界面设计
- 添加康养指数和知识图谱功能标签

### 未来规划
- AI模型本地化部署
- 高可用性集群部署
- 更多语言模型集成
- 高级分析功能扩展

---

**文档维护：** 海南长小养智能科技有限责任公司技术团队  
**技术支持：** <EMAIL>  
**项目仓库：** 私有仓库  

---

*此文档随系统版本更新，请定期检查最新版本。*