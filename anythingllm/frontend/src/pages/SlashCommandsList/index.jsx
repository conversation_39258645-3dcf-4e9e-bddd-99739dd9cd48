import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft, Copy, Star, Download, Plus, Trash, X } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import showToast from "@/utils/toast";
import paths from "@/utils/paths";

// 预定义的slash commands数据
const DEFAULT_SLASH_COMMANDS = [
  {
    id: "reset-chat",
    name: "/reset",
    description: "重置当前聊天对话，清除所有聊天历史",
    usage: "/reset",
    category: "聊天管理",
    author: "MHIIS团队",
    downloads: 1250,
    rating: 4.8,
    examples: [
      "用户：/reset",
      "系统：聊天历史已清除，开始新的对话。"
    ]
  },
  {
    id: "end-agent-session",
    name: "/exit",
    description: "结束当前Agent会话，返回普通聊天模式",
    usage: "/exit",
    category: "Agent管理",
    author: "MHIIS团队", 
    downloads: 980,
    rating: 4.7,
    examples: [
      "用户：/exit",
      "系统：Agent会话已结束，现在回到普通聊天模式。"
    ]
  },
  {
    id: "weather-query",
    name: "/weather",
    description: "查询指定城市的天气信息，支持气温、湿度、风速等数据",
    usage: "/weather [城市名称]",
    category: "信息查询",
    author: "气象数据团队",
    downloads: 2150,
    rating: 4.9,
    examples: [
      "用户：/weather 北京",
      "系统：北京今日天气：晴，气温15-25°C，湿度45%，东南风3级。"
    ]
  },
  {
    id: "health-index",
    name: "/health",
    description: "查询当前地区的健康指数和康养建议",
    usage: "/health [地区] [可选：具体日期]",
    category: "健康医疗",
    author: "康养指数团队",
    downloads: 1780,
    rating: 4.8,
    examples: [
      "用户：/health 上海",
      "系统：上海今日健康指数：优（85分），适宜户外运动，建议早晚散步。"
    ]
  },
  {
    id: "medical-search",
    name: "/medical",
    description: "搜索医疗相关信息，包括症状、药物、医院等",
    usage: "/medical [关键词]",
    category: "健康医疗", 
    author: "医疗知识团队",
    downloads: 3200,
    rating: 4.9,
    examples: [
      "用户：/medical 高血压症状",
      "系统：高血压常见症状包括：头痛、头晕、心悸、胸闷等..."
    ]
  },
  {
    id: "wellness-plan",
    name: "/wellness",
    description: "生成个性化康养计划和建议",
    usage: "/wellness [年龄] [健康状况] [目标]",
    category: "康养规划",
    author: "康养规划团队",
    downloads: 1920,
    rating: 4.7,
    examples: [
      "用户：/wellness 45 轻微高血压 改善睡眠",
      "系统：为您制定45岁高血压患者的睡眠改善康养计划..."
    ]
  },
  {
    id: "nutrition-advice",
    name: "/nutrition",
    description: "获取营养建议和饮食搭配方案",
    usage: "/nutrition [食物] 或 /nutrition [健康目标]",
    category: "营养健康",
    author: "营养师团队",
    downloads: 2450,
    rating: 4.8,
    examples: [
      "用户：/nutrition 减重饮食",
      "系统：减重期间建议：早餐燕麦+水果，午餐蛋白质+蔬菜..."
    ]
  },
  {
    id: "exercise-recommend",
    name: "/exercise",
    description: "推荐适合的运动方案和锻炼计划",
    usage: "/exercise [身体状况] [运动目标]",
    category: "运动健身",
    author: "运动康复团队",
    downloads: 1650,
    rating: 4.6,
    examples: [
      "用户：/exercise 办公族 缓解颈椎疼痛",
      "系统：办公族颈椎保健运动：颈部拉伸、肩胛骨运动..."
    ]
  }
];

const CATEGORIES = [
  "全部", "聊天管理", "Agent管理", "信息查询", 
  "健康医疗", "康养规划", "营养健康", "运动健身"
];

export default function SlashCommandsList() {
  const { t } = useTranslation();
  const [commands, setCommands] = useState([]);
  const [filteredCommands, setFilteredCommands] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("全部");
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("downloads"); // downloads, rating, name
  const [showAddModal, setShowAddModal] = useState(false);
  const [newCommand, setNewCommand] = useState({
    name: "",
    description: "",
    usage: "",
    category: "聊天管理",
    examples: []
  });

  // 初始化时从localStorage加载命令
  useEffect(() => {
    const savedCommands = localStorage.getItem('mhiis-slash-commands');
    if (savedCommands) {
      const parsed = JSON.parse(savedCommands);
      setCommands(parsed);
    } else {
      // 首次加载使用默认命令
      setCommands(DEFAULT_SLASH_COMMANDS);
      localStorage.setItem('mhiis-slash-commands', JSON.stringify(DEFAULT_SLASH_COMMANDS));
    }
  }, []);

  // 搜索和筛选逻辑
  useEffect(() => {
    let filtered = commands;

    // 分类筛选
    if (selectedCategory !== "全部") {
      filtered = filtered.filter(cmd => cmd.category === selectedCategory);
    }

    // 搜索筛选
    if (searchQuery) {
      filtered = filtered.filter(cmd => 
        cmd.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cmd.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cmd.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "rating":
          return b.rating - a.rating;
        case "name":
          return a.name.localeCompare(b.name);
        case "downloads":
        default:
          return b.downloads - a.downloads;
      }
    });

    setFilteredCommands(filtered);
  }, [commands, selectedCategory, searchQuery, sortBy]);

  const copyCommand = (command) => {
    navigator.clipboard.writeText(command.usage);
    showToast(`已复制指令：${command.usage}`, "success");
  };

  const installCommand = (command) => {
    // 这里可以实现安装逻辑
    showToast(`${command.name} 指令已添加到您的系统`, "success");
  };

  const deleteCommand = (commandId) => {
    const updatedCommands = commands.filter(cmd => cmd.id !== commandId);
    setCommands(updatedCommands);
    localStorage.setItem('mhiis-slash-commands', JSON.stringify(updatedCommands));
    showToast("指令已删除", "success");
  };

  const addCommand = () => {
    if (!newCommand.name || !newCommand.description || !newCommand.usage) {
      showToast("请填写完整的指令信息", "error");
      return;
    }

    const command = {
      id: `custom-${Date.now()}`,
      name: newCommand.name.startsWith('/') ? newCommand.name : `/${newCommand.name}`,
      description: newCommand.description,
      usage: newCommand.usage.startsWith('/') ? newCommand.usage : `/${newCommand.usage}`,
      category: newCommand.category,
      author: "自定义",
      downloads: 0,
      rating: 5.0,
      examples: newCommand.examples.filter(ex => ex.trim() !== '')
    };

    const updatedCommands = [...commands, command];
    setCommands(updatedCommands);
    localStorage.setItem('mhiis-slash-commands', JSON.stringify(updatedCommands));
    
    // 重置表单
    setNewCommand({
      name: "",
      description: "",
      usage: "",
      category: "聊天管理",
      examples: []
    });
    setShowAddModal(false);
    showToast("指令添加成功", "success");
  };

  const resetToDefaults = () => {
    setCommands(DEFAULT_SLASH_COMMANDS);
    localStorage.setItem('mhiis-slash-commands', JSON.stringify(DEFAULT_SLASH_COMMANDS));
    showToast("已重置为默认指令", "success");
  };

  return (
    <div className="w-full h-screen bg-theme-bg-primary overflow-y-auto">
      {/* 头部导航 */}
      <div className="bg-theme-bg-secondary border-b border-theme-modal-border">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4 mb-4">
            <Link 
              to={paths.home()}
              className="flex items-center gap-2 text-theme-text-secondary hover:text-theme-text-primary transition-colors"
            >
              <ArrowLeft size={20} />
              返回主页
            </Link>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">Slash Commands</h1>
              <p className="text-theme-text-secondary">
                发现和使用强大的斜杠指令来提升您的MHIIS体验
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-theme-text-secondary">
                共 {filteredCommands.length} 个指令
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => setShowAddModal(true)}
                  className="flex items-center gap-2 bg-primary-button hover:opacity-80 text-black px-4 py-2 rounded-lg text-sm font-medium transition-all"
                >
                  <Plus size={16} />
                  添加指令
                </button>
                <button
                  onClick={resetToDefaults}
                  className="flex items-center gap-2 bg-theme-bg-primary hover:bg-theme-action-menu-item-hover text-white px-4 py-2 rounded-lg text-sm transition-all border border-theme-modal-border"
                >
                  重置默认
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* 搜索和筛选栏 */}
        <div className="bg-theme-bg-secondary rounded-lg p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1">
              <input
                type="text"
                placeholder="搜索指令名称、描述或分类..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-theme-settings-input-bg border border-theme-modal-border rounded-lg px-4 py-2 text-white placeholder:text-theme-text-secondary focus:outline-none focus:border-primary-button"
              />
            </div>

            {/* 分类选择 */}
            <div className="flex flex-wrap gap-2">
              {CATEGORIES.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? "bg-primary-button text-black"
                      : "bg-theme-bg-primary text-theme-text-secondary hover:text-white hover:bg-theme-action-menu-item-hover"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>

            {/* 排序选择 */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-theme-settings-input-bg border border-theme-modal-border rounded-lg px-4 py-2 text-white focus:outline-none focus:border-primary-button"
            >
              <option value="downloads">按下载量排序</option>
              <option value="rating">按评分排序</option>
              <option value="name">按名称排序</option>
            </select>
          </div>
        </div>

        {/* 指令列表 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredCommands.map((command) => (
            <div key={command.id} className="bg-theme-bg-secondary border border-theme-modal-border rounded-lg p-6 hover:border-primary-button transition-colors">
              {/* 指令头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-white mb-1">{command.name}</h3>
                  <div className="flex items-center gap-4 text-sm text-theme-text-secondary">
                    <span className="bg-theme-bg-primary px-2 py-1 rounded text-xs">
                      {command.category}
                    </span>
                    <div className="flex items-center gap-1">
                      <Star size={14} className="text-yellow-400" />
                      <span>{command.rating}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Download size={14} />
                      <span>{command.downloads.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 描述 */}
              <p className="text-theme-text-secondary text-sm mb-4 line-clamp-3">
                {command.description}
              </p>

              {/* 用法示例 */}
              <div className="mb-4">
                <p className="text-xs text-theme-text-secondary mb-2">用法：</p>
                <code className="bg-theme-bg-primary px-3 py-2 rounded text-sm text-primary-button font-mono block">
                  {command.usage}
                </code>
              </div>

              {/* 示例对话 */}
              {command.examples && command.examples.length > 0 && (
                <div className="mb-4">
                  <p className="text-xs text-theme-text-secondary mb-2">示例：</p>
                  <div className="bg-theme-bg-primary rounded p-3 text-xs">
                    {command.examples.map((example, index) => (
                      <div key={index} className="mb-1 last:mb-0">
                        <span className={index % 2 === 0 ? "text-blue-400" : "text-green-400"}>
                          {example}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 作者 */}
              <p className="text-xs text-theme-text-secondary mb-4">
                作者：{command.author}
              </p>

              {/* 操作按钮 */}
              <div className="flex gap-2">
                <button
                  onClick={() => copyCommand(command)}
                  className="flex-1 flex items-center justify-center gap-2 bg-theme-bg-primary hover:bg-theme-action-menu-item-hover text-white px-4 py-2 rounded text-sm transition-colors"
                >
                  <Copy size={16} />
                  复制指令
                </button>
                <button
                  onClick={() => installCommand(command)}
                  className="flex-1 bg-primary-button hover:opacity-80 text-black px-4 py-2 rounded text-sm font-medium transition-all"
                >
                  添加到系统
                </button>
                {command.id.startsWith('custom-') && (
                  <button
                    onClick={() => deleteCommand(command.id)}
                    className="flex items-center justify-center bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm transition-colors"
                    title="删除自定义指令"
                  >
                    <Trash size={16} />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* 空状态 */}
        {filteredCommands.length === 0 && (
          <div className="text-center py-12">
            <p className="text-theme-text-secondary text-lg mb-4">
              没有找到匹配的指令
            </p>
            <p className="text-theme-text-secondary text-sm">
              尝试调整搜索条件或分类筛选
            </p>
          </div>
        )}
      </div>

      {/* 添加指令模态框 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-theme-bg-secondary border border-theme-modal-border rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">添加新指令</h2>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-theme-text-secondary hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="space-y-4">
              {/* 指令名称 */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  指令名称 <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  placeholder="例如：/weather 或 weather"
                  value={newCommand.name}
                  onChange={(e) => setNewCommand({...newCommand, name: e.target.value})}
                  className="w-full bg-theme-settings-input-bg border border-theme-modal-border rounded-lg px-4 py-2 text-white placeholder:text-theme-text-secondary focus:outline-none focus:border-primary-button"
                />
              </div>

              {/* 用法示例 */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  用法示例 <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  placeholder="例如：/weather [城市名称]"
                  value={newCommand.usage}
                  onChange={(e) => setNewCommand({...newCommand, usage: e.target.value})}
                  className="w-full bg-theme-settings-input-bg border border-theme-modal-border rounded-lg px-4 py-2 text-white placeholder:text-theme-text-secondary focus:outline-none focus:border-primary-button"
                />
              </div>

              {/* 描述 */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  描述 <span className="text-red-400">*</span>
                </label>
                <textarea
                  placeholder="详细描述这个指令的功能"
                  value={newCommand.description}
                  onChange={(e) => setNewCommand({...newCommand, description: e.target.value})}
                  rows={3}
                  className="w-full bg-theme-settings-input-bg border border-theme-modal-border rounded-lg px-4 py-2 text-white placeholder:text-theme-text-secondary focus:outline-none focus:border-primary-button resize-none"
                />
              </div>

              {/* 分类 */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  分类
                </label>
                <select
                  value={newCommand.category}
                  onChange={(e) => setNewCommand({...newCommand, category: e.target.value})}
                  className="w-full bg-theme-settings-input-bg border border-theme-modal-border rounded-lg px-4 py-2 text-white focus:outline-none focus:border-primary-button"
                >
                  {CATEGORIES.filter(cat => cat !== "全部").map((category) => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* 示例对话 */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">
                  示例对话 (可选)
                </label>
                <div className="space-y-2">
                  {newCommand.examples.map((example, index) => (
                    <div key={index} className="flex gap-2">
                      <input
                        type="text"
                        placeholder={index % 2 === 0 ? "用户：/weather 北京" : "系统：北京今日天气..."}
                        value={example}
                        onChange={(e) => {
                          const newExamples = [...newCommand.examples];
                          newExamples[index] = e.target.value;
                          setNewCommand({...newCommand, examples: newExamples});
                        }}
                        className="flex-1 bg-theme-settings-input-bg border border-theme-modal-border rounded-lg px-4 py-2 text-white placeholder:text-theme-text-secondary focus:outline-none focus:border-primary-button"
                      />
                      <button
                        onClick={() => {
                          const newExamples = newCommand.examples.filter((_, i) => i !== index);
                          setNewCommand({...newCommand, examples: newExamples});
                        }}
                        className="text-red-400 hover:text-red-300 transition-colors p-2"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={() => setNewCommand({...newCommand, examples: [...newCommand.examples, ""]})}
                    className="text-primary-button hover:opacity-80 text-sm transition-all"
                  >
                    + 添加示例
                  </button>
                </div>
              </div>
            </div>

            {/* 模态框底部按钮 */}
            <div className="flex gap-4 mt-6 pt-6 border-t border-theme-modal-border">
              <button
                onClick={() => setShowAddModal(false)}
                className="flex-1 bg-theme-bg-primary hover:bg-theme-action-menu-item-hover text-white px-4 py-2 rounded-lg text-sm transition-colors border border-theme-modal-border"
              >
                取消
              </button>
              <button
                onClick={addCommand}
                className="flex-1 bg-primary-button hover:opacity-80 text-black px-4 py-2 rounded-lg text-sm font-medium transition-all"
              >
                添加指令
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}