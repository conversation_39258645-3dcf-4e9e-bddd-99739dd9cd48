import React from "react";
import QuickLinks from "./QuickLinks";
import ExploreFeatures from "./ExploreFeatures";
import Checklist from "./Checklist";
import WorkspaceGrid from "./WorkspaceGrid";
import WeatherOverview from "./WeatherOverview";
import WeatherDataPanel from "./WeatherDataPanel";
import WeatherQuickLinks from "./WeatherQuickLinks";
import AdvancedFeatures from "./AdvancedFeatures";

export default function Home() {
  return (
    <div
      className="relative w-full min-h-full bg-theme-bg-primary print:h-auto print:text-sm print:min-h-0"
    >
      <div className="w-full min-h-full flex flex-col items-center overflow-y-auto no-scroll print:h-auto print:overflow-visible print:min-h-0">
        <div className="w-full max-w-[1600px] flex flex-col gap-y-[24px] p-6 pt-8 pb-16 print:gap-y-2 print:p-3 print:pt-2 print:pb-4" data-testid="main-content">
          {/* 系统介绍区域 */}
          <WeatherOverview />
          
          {/* 第一行：气象相关快捷链接+ 快捷链接*/}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 print:gap-2">
            <WeatherQuickLinks />
            <QuickLinks />
          </div>
          
          {/* 第二行：气象数据显示 + 我的工作区 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 print:gap-2">
            <WeatherDataPanel />
            <WorkspaceGrid />
          </div>
          
          {/* 第三行：气象进阶功能 + 进阶功能 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 print:gap-2">
            <AdvancedFeatures />
            <ExploreFeatures />
          </div>
          
          {/* 保持原有的其他组件 */}
          <div className="print:hidden">
            <Checklist />
          </div>
          
          {/* 打印专用版权信息 */}
          <div className="hidden print:block print:mt-8 print:pt-4 print:border-t print:border-gray-200 print:text-center">
            <p className="print:text-xs print:text-gray-600">
              © 2025 海南长小养智能科技有限责任公司
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}