import React, { useState, useEffect } from "react";
import { Plus } from "@phosphor-icons/react";
import WorkspaceRow from "../WorkspaceRow";
import NewWorkspaceModal, {
  useNewWorkspaceModal,
} from "@/components/Modals/NewWorkspace";
import Workspace from "@/models/workspace";
import showToast from "@/utils/toast";

export default function WorkspaceGrid() {
  const [workspaces, setWorkspaces] = useState([]);
  const [loading, setLoading] = useState(true);
  const { showing, showModal, hideModal } = useNewWorkspaceModal();

  // 加载工作区列表
  const loadWorkspaces = async () => {
    try {
      setLoading(true);
      const data = await Workspace.all();
      if (data) {
        setWorkspaces(data);
      }
    } catch (error) {
      console.error("Failed to load workspaces:", error);
      showToast("加载工作区失败", "error");
    } finally {
      setLoading(false);
    }
  };

  // 处理新工作区创建成功
  const handleWorkspaceCreated = (newWorkspace) => {
    setWorkspaces((prev) => [...prev, newWorkspace]);
  };

  useEffect(() => {
    loadWorkspaces();
  }, []);

  if (loading) {
    return (
      <div className="w-full bg-theme-bg-secondary rounded-xl border border-theme-modal-border p-8">
        <div className="flex items-center justify-center">
          <div className="text-theme-text-secondary">加载工作区中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-theme-bg-secondary rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-white mb-2">AI工作区</h2>
          <p className="text-theme-text-secondary text-sm">
            选择一个工作区开始对话，或创建新的工作区
          </p>
        </div>
        <button
          onClick={showModal}
          className="flex items-center gap-2 bg-primary-button hover:opacity-80 text-black px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300"
        >
          <Plus size={16} />
          新建工作区
        </button>
      </div>

      {workspaces.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-theme-text-secondary mb-4">还没有工作区</div>
          <button
            onClick={showModal}
            className="bg-transparent border border-primary-button text-primary-button hover:bg-primary-button hover:text-black px-6 py-3 rounded-lg transition-all duration-300"
          >
            创建第一个工作区
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {workspaces.map((workspace) => (
            <WorkspaceRow key={workspace.id} workspace={workspace} />
          ))}
        </div>
      )}

      {showing && (
        <NewWorkspaceModal
          hideModal={hideModal}
          onSuccess={handleWorkspaceCreated}
        />
      )}
    </div>
  );
}
