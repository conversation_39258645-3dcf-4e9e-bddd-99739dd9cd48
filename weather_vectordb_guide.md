# 气象数据录入向量库完整方案

## 1. 数据预处理和特征工程

### 1.1 数据清洗
```python
import pandas as pd
import numpy as np
from datetime import datetime

# 读取和清洗数据
def preprocess_weather_data(data):
    # 处理缺失值
    data = data.fillna({
        '降雨量（mm）': 0,
        '平均气温(℃)': data['平均气温(℃)'].mean(),
        '最高气温(℃)': data['最高气温(℃)'].mean(),
        '最低气温(℃)': data['最低气温(℃)'].mean(),
        '本站气压(hpa)': data['本站气压(hpa)'].mean(),
        '相对湿度（%）': data['相对湿度（%）'].mean()
    })
    
    # 数据类型转换
    numeric_columns = ['降雨量（mm）', '平均气温(℃)', '最高气温(℃)', 
                      '最低气温(℃)', '本站气压(hpa)', '相对湿度（%）']
    for col in numeric_columns:
        data[col] = pd.to_numeric(data[col], errors='coerce')
    
    return data
```

### 1.2 特征工程
```python
def create_weather_features(data):
    # 基础特征
    data['温差'] = data['最高气温(℃)'] - data['最低气温(℃)']
    data['是否降雨'] = (data['降雨量（mm）'] > 0).astype(int)
    
    # 时间特征
    data['完整日期'] = pd.to_datetime(data['完整日期'])
    data['星期几'] = data['完整日期'].dt.dayofweek
    data['是否周末'] = (data['星期几'] >= 5).astype(int)
    data['季节'] = data['月份'].map({12:4, 1:4, 2:4, 3:1, 4:1, 5:1, 
                                   6:2, 7:2, 8:2, 9:3, 10:3, 11:3})
    
    # 滑动窗口特征（3日均值）
    data['3日平均气温'] = data['平均气温(℃)'].rolling(window=3, center=True).mean()
    data['3日平均降雨'] = data['降雨量（mm）'].rolling(window=3, center=True).mean()
    data['3日平均湿度'] = data['相对湿度（%）'].rolling(window=3, center=True).mean()
    
    return data
```

## 2. 向量化策略

### 2.1 数值特征向量化
```python
from sklearn.preprocessing import StandardScaler, MinMaxScaler

def create_numerical_vectors(data):
    # 选择数值特征
    numerical_features = [
        '降雨量（mm）', '平均气温(℃)', '最高气温(℃)', '最低气温(℃)',
        '本站气压(hpa)', '相对湿度（%）', '温差', '3日平均气温', 
        '3日平均降雨', '3日平均湿度'
    ]
    
    # 标准化处理
    scaler = StandardScaler()
    numerical_vectors = scaler.fit_transform(data[numerical_features])
    
    return numerical_vectors, scaler
```

### 2.2 文本描述向量化
```python
def create_weather_descriptions(data):
    descriptions = []
    for _, row in data.iterrows():
        # 创建详细的天气描述
        desc = f"""
        日期：{row['完整日期'].strftime('%Y年%m月%d日')}
        地点：海南省琼海市国家气象站
        天气概况：平均气温{row['平均气温(℃)']}℃，最高气温{row['最高气温(℃)']}℃，最低气温{row['最低气温(℃)']}℃
        降水情况：{'有降雨' if row['降雨量（mm）'] > 0 else '无降雨'}，降雨量{row['降雨量（mm）']}毫米
        大气条件：气压{row['本站气压(hpa)']}百帕，相对湿度{row['相对湿度（%）']}%
        季节特征：{'春季' if row['季节']==1 else '夏季' if row['季节']==2 else '秋季' if row['季节']==3 else '冬季'}
        温差特征：日温差{row['温差']:.1f}℃
        """
        descriptions.append(desc.strip())
    
    return descriptions

# 使用sentence-transformers进行文本向量化
from sentence_transformers import SentenceTransformer

def create_text_vectors(descriptions):
    model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
    text_vectors = model.encode(descriptions)
    return text_vectors
```

## 3. 向量库选择和配置

### 3.1 使用Chroma向量库
```python
import chromadb
from chromadb.config import Settings

# 初始化Chroma客户端
client = chromadb.Client(Settings(
    chroma_db_impl="duckdb+parquet",
    persist_directory="./weather_vectordb"
))

# 创建集合
collection = client.create_collection(
    name="qionghai_weather_2024",
    metadata={"description": "2024年琼海国家气象站逐日气象数据"}
)
```

### 3.2 使用Pinecone向量库
```python
import pinecone

# 初始化Pinecone
pinecone.init(api_key="your-api-key", environment="your-env")

# 创建索引
index_name = "qionghai-weather-2024"
if index_name not in pinecone.list_indexes():
    pinecone.create_index(
        name=index_name,
        dimension=384,  # 根据embedding维度调整
        metric="cosine"
    )

index = pinecone.Index(index_name)
```

## 4. 数据录入实现

### 4.1 完整录入流程
```python
def insert_weather_data_to_vectordb(data, collection_type="chroma"):
    # 1. 数据预处理
    processed_data = preprocess_weather_data(data)
    processed_data = create_weather_features(processed_data)
    
    # 2. 创建向量
    descriptions = create_weather_descriptions(processed_data)
    text_vectors = create_text_vectors(descriptions)
    numerical_vectors, scaler = create_numerical_vectors(processed_data)
    
    # 3. 录入向量库
    if collection_type == "chroma":
        insert_to_chroma(processed_data, text_vectors, descriptions)
    elif collection_type == "pinecone":
        insert_to_pinecone(processed_data, text_vectors, descriptions)
    
    return scaler  # 保存scaler用于后续查询

def insert_to_chroma(data, vectors, descriptions):
    documents = []
    metadatas = []
    ids = []
    
    for i, (_, row) in enumerate(data.iterrows()):
        # 创建文档
        documents.append(descriptions[i])
        
        # 创建元数据
        metadatas.append({
            "date": row['完整日期'].isoformat(),
            "rainfall": float(row['降雨量（mm）']),
            "avg_temp": float(row['平均气温(℃)']),
            "max_temp": float(row['最高气温(℃)']),
            "min_temp": float(row['最低气温(℃)']),
            "pressure": float(row['本站气压(hpa)']),
            "humidity": float(row['相对湿度（%）']),
            "season": int(row['季节']),
            "month": int(row['月份']),
            "is_weekend": int(row['是否周末'])
        })
        
        # 创建ID
        ids.append(f"weather_{row['完整日期'].strftime('%Y%m%d')}")
    
    # 批量插入
    collection.add(
        documents=documents,
        metadatas=metadatas,
        ids=ids,
        embeddings=vectors.tolist()
    )

def insert_to_pinecone(data, vectors, descriptions):
    vectors_to_upsert = []
    
    for i, (_, row) in enumerate(data.iterrows()):
        vector_data = {
            "id": f"weather_{row['完整日期'].strftime('%Y%m%d')}",
            "values": vectors[i].tolist(),
            "metadata": {
                "date": row['完整日期'].isoformat(),
                "description": descriptions[i],
                "rainfall": float(row['降雨量（mm）']),
                "avg_temp": float(row['平均气温(℃)']),
                "max_temp": float(row['最高气温(℃)']),
                "min_temp": float(row['最低气温(℃)']),
                "pressure": float(row['本站气压(hpa)']),
                "humidity": float(row['相对湿度（%）']),
                "season": int(row['季节'])
            }
        }
        vectors_to_upsert.append(vector_data)
    
    # 批量插入
    index.upsert(vectors=vectors_to_upsert)
```

## 5. 查询和检索功能

### 5.1 相似性查询
```python
def query_similar_weather(query_text, collection, top_k=5):
    # 向量化查询文本
    model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
    query_vector = model.encode([query_text])
    
    # 执行查询
    results = collection.query(
        query_embeddings=query_vector.tolist(),
        n_results=top_k,
        include=["documents", "metadatas", "distances"]
    )
    
    return results

# 使用示例
query = "寻找2024年夏季高温且降雨量大的天气"
results = query_similar_weather(query, collection)
```

### 5.2 条件筛选查询
```python
def query_weather_by_conditions(collection, conditions, top_k=10):
    # 根据条件筛选
    results = collection.query(
        query_texts=["天气查询"],
        where=conditions,
        n_results=top_k,
        include=["documents", "metadatas"]
    )
    
    return results

# 使用示例：查找高温天气
high_temp_results = query_weather_by_conditions(
    collection,
    {"max_temp": {"$gte": 35}},  # 最高气温>=35℃
    top_k=10
)
```

## 6. 实际应用场景

### 6.1 天气模式分析
- 相似天气日期查找
- 极端天气事件检索
- 季节性规律发现

### 6.2 预测和预警
- 基于历史相似天气的短期预测
- 异常天气模式识别
- 灾害天气预警

### 6.3 农业应用
- 适宜播种/收获日期推荐
- 灌溉需求预测
- 病虫害风险评估

## 7. 性能优化建议

### 7.1 索引优化
- 为常用查询字段创建索引
- 使用合适的向量维度（256-1024）
- 定期重建和优化索引

### 7.2 查询优化
- 使用批量查询减少网络开销
- 实现查询结果缓存
- 采用分层查询策略

### 7.3 存储优化
- 数据分片存储（按月/季度）
- 压缩历史数据
- 备份和恢复策略

## 8. 部署和监控

### 8.1 部署架构
```python
# Docker部署示例
version: '3.8'
services:
  vectordb:
    image: chromadb/chroma:latest
    ports:
      - "8000:8000"
    volumes:
      - ./data:/chroma/chroma
    environment:
      - CHROMA_DB_IMPL=clickhouse
```

### 8.2 监控指标
- 查询响应时间
- 存储空间使用率
- 索引更新频率
- 查询准确率

这个方案提供了从数据预处理到部署监控的完整流程，可以根据具体需求进行调整和优化。