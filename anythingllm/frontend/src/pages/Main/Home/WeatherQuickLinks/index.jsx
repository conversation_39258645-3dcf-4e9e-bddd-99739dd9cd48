import React from "react";
import { 
  CloudRain, 
  TrendUp, 
  Gauge,
  Activity,
  Calendar
} from "@phosphor-icons/react";

export default function WeatherQuickLinks() {
  return (
    <div className="w-full bg-theme-bg-secondary rounded-xl p-6">
      <div className="flex items-center gap-2 mb-6">
        <CloudRain size={20} className="text-blue-400" />
        <h3 className="text-lg font-medium text-white">气象相关快捷链接</h3>
      </div>
      
      <div className="space-y-3">
        <div 
          className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover 
                     rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group"
        >
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <TrendUp size={18} className="text-blue-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">气象数据实时监控</h4>
              <p className="text-theme-text-secondary text-xs">
                实时获取和分析温度、湿度、气压等气象数据
              </p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>

        <div 
          className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover 
                     rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group"
        >
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <Gauge size={18} className="text-blue-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">气象预测分析</h4>
              <p className="text-theme-text-secondary text-xs">
                基于AI模型预测未来气象变化趋势
              </p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>

        <div 
          className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover 
                     rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group"
        >
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <Activity size={18} className="text-green-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">健康指数分析</h4>
              <p className="text-theme-text-secondary text-xs">
                基于气象数据计算健康舒适度指数
              </p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>

        <div 
          className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover 
                     rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group"
        >
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <Calendar size={18} className="text-purple-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">康养计划制定</h4>
              <p className="text-theme-text-secondary text-xs">
                根据气象预测制定个性化康养方案
              </p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>
      </div>
    </div>
  );
}