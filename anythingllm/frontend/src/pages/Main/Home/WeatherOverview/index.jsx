import React from "react";
import { 
  CloudRain, 
  Activity, 
  Gauge,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>
} from "@phosphor-icons/react";

export default function WeatherOverview() {
  return (
    <div className="w-full bg-theme-bg-secondary rounded-xl p-8 print:p-4">
      <div className="text-center mb-8 print:mb-4">
        <div className="flex items-center justify-center gap-3 mb-6 print:mb-3">
          <div className="flex gap-2">
            <Brain className="w-8 h-8 text-blue-400 print:w-6 print:h-6" />
            <Heartbeat className="w-8 h-8 text-green-400 print:w-6 print:h-6" />
            <Leaf className="w-8 h-8 text-purple-400 print:w-6 print:h-6" />
          </div>
          <h2 className="text-3xl font-bold text-white print:text-2xl">医疗气候康养智能分析系统 MHIIS</h2>
        </div>
        <p className="text-theme-text-secondary text-lg mb-8 print:text-base print:mb-4">
          基于多模态大模型构建的专业医疗气候康养智能分析平台，整合时序知识图谱技术
        </p>
      </div>

      {/* 核心功能模块 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 print:gap-3 print:mb-4">
        <FeatureBlock
          icon={<CloudRain size={32} className="text-blue-400" />}
          title="气象监控"
          description="实时气象数据采集与分析"
          bgColor="bg-blue-400/10"
          borderColor="border-blue-400/30"
        />
        <FeatureBlock
          icon={<Gauge size={32} className="text-green-400" />}
          title="智能预测"
          description="AI驱动的气象趋势预测"
          bgColor="bg-green-400/10"
          borderColor="border-green-400/30"
        />
        <FeatureBlock
          icon={<Activity size={32} className="text-yellow-400" />}
          title="康养分析"
          description="个性化康养指数评估"
          bgColor="bg-yellow-400/10"
          borderColor="border-yellow-400/30"
        />
        <FeatureBlock
          icon={<Leaf size={32} className="text-purple-400" />}
          title="知识图谱"
          description="时序知识图谱智能推荐"
          bgColor="bg-purple-400/10"
          borderColor="border-purple-400/30"
        />
      </div>

    </div>
  );
}

function FeatureBlock({ icon, title, description, bgColor, borderColor }) {
  return (
    <div className={`${bgColor} ${borderColor} border rounded-lg p-4 text-center hover:scale-105 transition-all duration-300`}>
      <div className="flex justify-center mb-3">{icon}</div>
      <h3 className="text-white font-semibold text-sm mb-2">{title}</h3>
      <p className="text-theme-text-secondary text-xs">{description}</p>
    </div>
  );
}

