import React from "react";
import { 
  <PERSON>, 
  <PERSON>,
  MagnifyingGlass,
  Upload,
  Chart<PERSON><PERSON>,
  <PERSON>raph,
  <PERSON>
} from "@phosphor-icons/react";

export default function AdvancedFeatures() {
  return (
    <div className="w-full bg-theme-bg-secondary rounded-xl p-6">
      <div className="flex items-center gap-2 mb-6">
        <Lightning size={24} className="text-blue-400" />
        <h3 className="text-xl font-semibold text-white">气象进阶功能</h3>
      </div>
      
      <div className="space-y-3">
        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group">
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <Upload size={18} className="text-blue-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">数据导入</h4>
              <p className="text-theme-text-secondary text-xs">批量导入气象数据</p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>

        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group">
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <ChartBar size={18} className="text-green-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">数据可视化</h4>
              <p className="text-theme-text-secondary text-xs">生成图表和报告</p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>

        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group">
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <Brain size={18} className="text-purple-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">AI预测</h4>
              <p className="text-theme-text-secondary text-xs">智能气象预测分析</p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>

        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group">
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <MagnifyingGlass size={18} className="text-orange-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">数据检索</h4>
              <p className="text-theme-text-secondary text-xs">快速查找历史数据</p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>

        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group">
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <Graph size={18} className="text-cyan-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">知识图谱</h4>
              <p className="text-theme-text-secondary text-xs">时序知识图谱智能关联分析</p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>

        <div className="bg-theme-bg-secondary border border-theme-modal-border/30 hover:bg-theme-action-menu-item-hover rounded-lg p-4 cursor-pointer transition-all duration-300 flex items-center justify-between group">
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-theme-bg-secondary rounded-lg">
              <Bell size={18} className="text-red-400" />
            </div>
            <div className="flex-1">
              <h4 className="text-white font-medium text-sm mb-1">预警通知</h4>
              <p className="text-theme-text-secondary text-xs">异常气象条件智能预警提醒</p>
            </div>
          </div>
          <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-xs">
            →
          </div>
        </div>
      </div>
    </div>
  );
}