import React from "react";
import SettingsButton from "../SettingsButton";
import { isMobile } from "react-device-detect";
import {
  BookOpen,
  DiscordLogo,
  GithubLogo,
  Briefcase,
  Envelope,
  Globe,
  HouseLine,
  Info,
  LinkSimple,
} from "@phosphor-icons/react";

export const MAX_ICONS = 3;
export const ICON_COMPONENTS = {
  BookOpen: BookOpen,
  DiscordLogo: DiscordLogo,
  GithubLogo: GithubLogo,
  Envelope: Envelope,
  LinkSimple: LinkSimple,
  HouseLine: HouseLine,
  Globe: Globe,
  Briefcase: Briefcase,
  Info: Info,
};

export default function Footer() {
  return (
    <div className="flex flex-col items-center mb-2">
      <div className="flex space-x-4 mb-2">{!isMobile && <SettingsButton />}</div>
      
      {/* 版权信息 - 仅在打印时显示 */}
      <div className="hidden print:block text-center">
        <p className="text-xs text-gray-600">
          © 2025 海南长小养智能科技有限责任公司
        </p>
        <p className="text-xs text-gray-600">
          医疗气候康养智能分析系统 MHIIS
        </p>
      </div>
    </div>
  );
}
