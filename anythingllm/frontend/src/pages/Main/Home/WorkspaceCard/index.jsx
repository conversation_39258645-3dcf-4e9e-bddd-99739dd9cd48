import React from "react";
import { ChatText, Users, Calendar } from "@phosphor-icons/react";
import paths from "@/utils/paths";
import { formatBeijingTime } from "@/utils/dateFormatter";

export default function WorkspaceCard({ workspace, onClick }) {
  const handleClick = () => {
    if (onClick) {
      onClick(workspace);
    } else {
      // 默认行为：跳转到工作区聊天
      window.location.href = paths.workspace.chat(workspace.slug);
    }
  };

  return (
    <div
      className="bg-theme-bg-secondary border border-theme-modal-border rounded-lg p-6 hover:bg-theme-action-menu-item-hover transition-all duration-300 cursor-pointer group"
      onClick={handleClick}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-primary-button transition-colors">
            {workspace.name}
          </h3>
          <p className="text-theme-text-secondary text-sm line-clamp-2">
            {workspace.description || "暂无描述"}
          </p>
        </div>
        <div className="ml-4 p-2 bg-theme-bg-primary rounded-lg group-hover:bg-primary-button group-hover:text-black transition-all duration-300">
          <ChatText
            size={20}
            className="text-theme-text-primary group-hover:text-black"
          />
        </div>
      </div>

      <div className="flex items-center justify-between text-xs text-theme-text-secondary">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <Users size={14} />
            <span>{workspace.chats?.length || 0} 对话</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar size={14} />
            <span>
              {workspace.createdAt
                ? formatBeijingTime(workspace.createdAt, false)
                : "最近创建"}
            </span>
          </div>
        </div>
        <div className="text-primary-button opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          点击进入 →
        </div>
      </div>
    </div>
  );
}
